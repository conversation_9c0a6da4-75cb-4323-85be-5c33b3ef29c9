import XCTest
@testable import TamagotchiApp

final class TamagotchiAppTests: XCTestCase {

    override func setUpWithError() throws {
        // Put setup code here. This method is called before the invocation of each test method in the class.
    }

    override func tearDownWithError() throws {
        // Put teardown code here. This method is called after the invocation of each test method in the class.
    }

    // MARK: - Pet Tests
    
    func testPetInitialization() throws {
        let pet = Pet(name: "测试宠物")
        
        XCTAssertEqual(pet.name, "测试宠物")
        XCTAssertEqual(pet.stage, .egg)
        XCTAssertEqual(pet.mood, .normal)
        XCTAssertEqual(pet.hunger, 50)
        XCTAssertEqual(pet.happiness, 50)
        XCTAssertEqual(pet.health, 100)
        XCTAssertEqual(pet.energy, 100)
        XCTAssertEqual(pet.age, 0)
        XCTAssertEqual(pet.totalFeedings, 0)
        XCTAssertEqual(pet.totalInteractions, 0)
    }
    
    func testPetFeeding() throws {
        let pet = Pet()
        pet.stage = .baby // 设置为可以喂食的阶段
        
        let initialHunger = pet.hunger
        let initialFeedings = pet.totalFeedings
        
        pet.feed()
        
        XCTAssertLessThan(pet.hunger, initialHunger)
        XCTAssertEqual(pet.totalFeedings, initialFeedings + 1)
    }
    
    func testPetPlay() throws {
        let pet = Pet()
        pet.stage = .baby
        
        let initialHappiness = pet.happiness
        let initialInteractions = pet.totalInteractions
        
        pet.play()
        
        XCTAssertGreaterThan(pet.happiness, initialHappiness)
        XCTAssertEqual(pet.totalInteractions, initialInteractions + 1)
    }
    
    func testPetStageProgression() throws {
        let pet = Pet()
        
        // 测试从蛋到幼体
        XCTAssertEqual(pet.stage, .egg)
        
        pet.age = 1
        pet.updateStatus()
        XCTAssertEqual(pet.stage, .baby)
        
        // 测试从幼体到儿童
        pet.age = 24
        pet.updateStatus()
        XCTAssertEqual(pet.stage, .child)
        
        // 测试从儿童到青少年
        pet.age = 72
        pet.updateStatus()
        XCTAssertEqual(pet.stage, .teen)
        
        // 测试从青少年到成年
        pet.age = 168
        pet.updateStatus()
        XCTAssertEqual(pet.stage, .adult)
        
        // 测试从成年到老年
        pet.age = 720
        pet.updateStatus()
        XCTAssertEqual(pet.stage, .elder)
    }
    
    func testPetMoodUpdate() throws {
        let pet = Pet()
        
        // 测试生病状态
        pet.health = 20
        pet.updateMood()
        XCTAssertEqual(pet.mood, .sick)
        
        // 测试困倦状态
        pet.health = 100
        pet.energy = 10
        pet.updateMood()
        XCTAssertEqual(pet.mood, .sleeping)
        
        // 测试开心状态
        pet.energy = 100
        pet.happiness = 80
        pet.hunger = 20
        pet.updateMood()
        XCTAssertEqual(pet.mood, .happy)
        
        // 测试伤心状态
        pet.happiness = 20
        pet.hunger = 80
        pet.updateMood()
        XCTAssertEqual(pet.mood, .sad)
    }
    
    // MARK: - Food Tests
    
    func testFoodEffects() throws {
        let apple = Food(type: .apple)
        XCTAssertEqual(apple.effect.hungerChange, -20)
        XCTAssertEqual(apple.effect.healthChange, 10)
        
        let candy = Food(type: .candy)
        XCTAssertEqual(candy.effect.happinessChange, 20)
        XCTAssertEqual(candy.effect.healthChange, -5)
        
        let medicine = Food(type: .medicine)
        XCTAssertEqual(apple.effect.healthChange, 10)
    }
    
    func testFeedingManager() throws {
        let pet = Pet()
        pet.stage = .baby
        pet.hunger = 80
        
        let bread = Food(type: .bread)
        let result = FeedingManager.feedPet(pet, with: bread)
        
        XCTAssertTrue(result.success)
        XCTAssertLessThan(pet.hunger, 80)
        XCTAssertEqual(pet.totalFeedings, 1)
    }
    
    func testFeedingEgg() throws {
        let pet = Pet() // 默认是蛋状态
        let apple = Food(type: .apple)
        
        let result = FeedingManager.feedPet(pet, with: apple)
        
        XCTAssertFalse(result.success)
        XCTAssertEqual(pet.totalFeedings, 0)
    }
    
    // MARK: - Interaction Tests
    
    func testInteractionEffects() throws {
        let pet = Interaction(type: .pet)
        XCTAssertEqual(pet.effect.happinessChange, 15)
        
        let play = Interaction(type: .play)
        XCTAssertEqual(play.effect.happinessChange, 25)
        XCTAssertEqual(play.effect.energyChange, -15)
        
        let clean = Interaction(type: .clean)
        XCTAssertEqual(clean.effect.healthChange, 15)
    }
    
    func testInteractionManager() throws {
        let pet = Pet()
        pet.stage = .baby
        pet.happiness = 50
        
        let petInteraction = Interaction(type: .pet)
        let result = InteractionManager.interactWithPet(pet, interaction: petInteraction)
        
        XCTAssertTrue(result.success)
        XCTAssertGreaterThan(pet.happiness, 50)
        XCTAssertEqual(pet.totalInteractions, 1)
    }
    
    func testInteractionWithEgg() throws {
        let pet = Pet() // 默认是蛋状态
        let petInteraction = Interaction(type: .pet)
        
        let result = InteractionManager.interactWithPet(pet, interaction: petInteraction)
        
        XCTAssertFalse(result.success)
        XCTAssertEqual(pet.totalInteractions, 0)
    }
    
    func testInteractionEnergyRequirement() throws {
        let pet = Pet()
        pet.stage = .baby
        pet.energy = 10 // 低精力
        
        let playInteraction = Interaction(type: .play) // 需要20精力
        let result = InteractionManager.interactWithPet(pet, interaction: playInteraction)
        
        XCTAssertFalse(result.success)
    }
    
    // MARK: - GameManager Tests
    
    func testGameManagerInitialization() throws {
        let gameManager = GameManager()
        
        XCTAssertNotNil(gameManager.pet)
        XCTAssertEqual(gameManager.pet.name, "我的宠物")
    }
    
    func testPetHatching() throws {
        let gameManager = GameManager()
        
        XCTAssertEqual(gameManager.pet.stage, .egg)
        
        gameManager.hatchPet()
        
        XCTAssertEqual(gameManager.pet.age, 1)
        XCTAssertEqual(gameManager.pet.stage, .baby)
    }
    
    // MARK: - Performance Tests
    
    func testPetUpdatePerformance() throws {
        let pet = Pet()
        
        measure {
            for _ in 0..<1000 {
                pet.updateStatus()
            }
        }
    }
    
    func testFeedingPerformance() throws {
        let pet = Pet()
        pet.stage = .baby
        let food = Food(type: .apple)
        
        measure {
            for _ in 0..<1000 {
                _ = FeedingManager.feedPet(pet, with: food)
            }
        }
    }
    
    // MARK: - Data Persistence Tests
    
    func testPetCodable() throws {
        let originalPet = Pet(name: "测试宠物")
        originalPet.age = 100
        originalPet.happiness = 75
        originalPet.stage = .adult
        
        // 编码
        let encodedData = try JSONEncoder().encode(originalPet)
        
        // 解码
        let decodedPet = try JSONDecoder().decode(Pet.self, from: encodedData)
        
        XCTAssertEqual(decodedPet.name, originalPet.name)
        XCTAssertEqual(decodedPet.age, originalPet.age)
        XCTAssertEqual(decodedPet.happiness, originalPet.happiness)
        XCTAssertEqual(decodedPet.stage, originalPet.stage)
    }
}
