#!/bin/bash

echo "🧪 拓麻歌子应用构建测试"
echo "=========================="

# 检查Xcode安装
echo "1. 检查开发环境..."
if command -v xcodebuild &> /dev/null; then
    echo "✅ xcodebuild 已安装"
    xcodebuild -version 2>/dev/null || echo "⚠️  需要完整的Xcode，当前只有Command Line Tools"
else
    echo "❌ xcodebuild 未找到"
fi

# 检查Swift编译器
echo ""
echo "2. 检查Swift编译器..."
if command -v swift &> /dev/null; then
    echo "✅ Swift 编译器已安装"
    swift --version
else
    echo "❌ Swift 编译器未找到"
fi

# 检查项目文件结构
echo ""
echo "3. 检查项目文件结构..."
if [ -f "TamagotchiApp.xcodeproj/project.pbxproj" ]; then
    echo "✅ Xcode项目文件存在"
else
    echo "❌ Xcode项目文件不存在"
fi

if [ -d "TamagotchiApp" ]; then
    echo "✅ 应用源码目录存在"
    echo "   文件数量: $(find TamagotchiApp -name "*.swift" | wc -l) 个Swift文件"
else
    echo "❌ 应用源码目录不存在"
fi

# 检查关键文件
echo ""
echo "4. 检查关键文件..."
key_files=(
    "TamagotchiApp/TamagotchiAppApp.swift"
    "TamagotchiApp/ContentView.swift"
    "TamagotchiApp/Models/Pet.swift"
    "TamagotchiApp/Models/GameManager.swift"
    "TamagotchiApp/Views/PetView.swift"
)

for file in "${key_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file"
    fi
done

# 尝试语法检查（如果有Swift编译器）
echo ""
echo "5. Swift语法检查..."
if command -v swift &> /dev/null; then
    echo "正在检查Swift文件语法..."
    
    # 检查主要Swift文件
    for swift_file in $(find TamagotchiApp -name "*.swift" 2>/dev/null); do
        if swift -frontend -parse "$swift_file" &>/dev/null; then
            echo "✅ $swift_file - 语法正确"
        else
            echo "❌ $swift_file - 语法错误"
        fi
    done
else
    echo "⚠️  无法进行语法检查，需要Swift编译器"
fi

echo ""
echo "6. 安装建议..."
echo "要运行iOS模拟器，请安装以下组件："
echo ""
echo "📱 Xcode (必需):"
echo "   - 从Mac App Store安装Xcode"
echo "   - 或从 https://developer.apple.com/xcode/ 下载"
echo "   - 大小: ~12-15GB"
echo ""
echo "🔧 安装后的步骤:"
echo "   1. 打开Xcode"
echo "   2. 同意许可协议"
echo "   3. 安装额外组件"
echo "   4. 打开项目: TamagotchiApp.xcodeproj"
echo "   5. 选择iOS模拟器设备"
echo "   6. 点击运行按钮 (▶️)"
echo ""
echo "📋 替代方案 (如果无法安装Xcode):"
echo "   - 使用在线Swift Playground"
echo "   - 使用SwiftUI预览功能"
echo "   - 在真实iOS设备上测试 (需要开发者账号)"

echo ""
echo "=========================="
echo "测试完成！"
