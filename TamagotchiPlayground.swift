import SwiftUI
import Foundation

// MARK: - 简化的宠物模型
class SimplePet: ObservableObject {
    @Published var hunger: Int = 50
    @Published var happiness: Int = 50
    @Published var health: Int = 100
    @Published var age: Int = 0
    @Published var stage: PetStage = .egg
    @Published var mood: PetMood = .normal
    @Published var isAlive: Bool = true
    
    enum PetStage: String, CaseIterable {
        case egg = "🥚"
        case baby = "🐣"
        case child = "🐤"
        case teen = "🐦"
        case adult = "🦅"
        
        var name: String {
            switch self {
            case .egg: return "蛋"
            case .baby: return "幼体"
            case .child: return "儿童"
            case .teen: return "青少年"
            case .adult: return "成年"
            }
        }
    }
    
    enum PetMood: String, CaseIterable {
        case happy = "😊"
        case normal = "😐"
        case sad = "😢"
        case sick = "🤒"
        case sleeping = "😴"
        
        var name: String {
            switch self {
            case .happy: return "开心"
            case .normal: return "正常"
            case .sad: return "伤心"
            case .sick: return "生病"
            case .sleeping: return "困倦"
            }
        }
    }
    
    func feed() {
        hunger = max(0, hunger - 20)
        happiness = min(100, happiness + 10)
        updateMood()
    }
    
    func play() {
        happiness = min(100, happiness + 20)
        hunger = min(100, hunger + 10)
        updateMood()
    }
    
    func heal() {
        health = min(100, health + 30)
        updateMood()
    }
    
    func hatch() {
        if stage == .egg {
            stage = .baby
        }
    }
    
    func grow() {
        age += 1
        if age > 10 && stage == .baby { stage = .child }
        else if age > 20 && stage == .child { stage = .teen }
        else if age > 30 && stage == .teen { stage = .adult }
        updateMood()
    }
    
    private func updateMood() {
        if health < 30 {
            mood = .sick
        } else if hunger > 80 {
            mood = .sad
        } else if happiness > 70 {
            mood = .happy
        } else if happiness < 30 {
            mood = .sad
        } else {
            mood = .normal
        }
    }
}

// MARK: - 主界面
struct TamagotchiPlaygroundView: View {
    @StateObject private var pet = SimplePet()
    @State private var showingStats = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("🐾 拓麻歌子")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                // 宠物显示区域
                VStack {
                    Text(pet.stage.rawValue)
                        .font(.system(size: 100))
                        .scaleEffect(pet.mood == .happy ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.5), value: pet.mood)
                        .onTapGesture {
                            if pet.stage == .egg {
                                pet.hatch()
                            }
                        }
                    
                    Text(pet.stage.name)
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text(pet.mood.name + " " + pet.mood.rawValue)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.blue.opacity(0.1))
                )
                
                // 状态条
                VStack(spacing: 10) {
                    StatusBar(title: "饥饿度", value: pet.hunger, color: .orange)
                    StatusBar(title: "快乐度", value: pet.happiness, color: .green)
                    StatusBar(title: "健康度", value: pet.health, color: .red)
                }
                .padding(.horizontal)
                
                // 操作按钮
                if pet.stage != .egg {
                    VStack(spacing: 15) {
                        HStack(spacing: 20) {
                            ActionButton(title: "喂食", icon: "🍎") {
                                pet.feed()
                            }
                            
                            ActionButton(title: "游戏", icon: "🎾") {
                                pet.play()
                            }
                        }
                        
                        HStack(spacing: 20) {
                            ActionButton(title: "治疗", icon: "💊") {
                                pet.heal()
                            }
                            
                            ActionButton(title: "成长", icon: "⬆️") {
                                pet.grow()
                            }
                        }
                    }
                } else {
                    Text("点击蛋来孵化你的宠物！")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .padding()
                }
                
                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("统计") {
                        showingStats = true
                    }
                }
            }
            .sheet(isPresented: $showingStats) {
                StatsView(pet: pet)
            }
        }
    }
}

// MARK: - 状态条组件
struct StatusBar: View {
    let title: String
    let value: Int
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                Spacer()
                Text("\(value)/100")
                    .font(.caption)
                    .fontWeight(.bold)
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 8)
                        .cornerRadius(4)
                    
                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * CGFloat(value) / 100, height: 8)
                        .cornerRadius(4)
                        .animation(.easeInOut(duration: 0.3), value: value)
                }
            }
            .frame(height: 8)
        }
    }
}

// MARK: - 操作按钮组件
struct ActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Text(icon)
                    .font(.title2)
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .frame(width: 80, height: 60)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 统计页面
struct StatsView: View {
    @ObservedObject var pet: SimplePet
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("宠物统计")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                VStack(spacing: 15) {
                    StatRow(title: "年龄", value: "\(pet.age) 天")
                    StatRow(title: "阶段", value: pet.stage.name)
                    StatRow(title: "心情", value: pet.mood.name)
                    StatRow(title: "饥饿度", value: "\(pet.hunger)/100")
                    StatRow(title: "快乐度", value: "\(pet.happiness)/100")
                    StatRow(title: "健康度", value: "\(pet.health)/100")
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(15)
                
                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

struct StatRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .fontWeight(.medium)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - 应用入口
struct ContentView: View {
    var body: some View {
        TamagotchiPlaygroundView()
    }
}

// 预览
struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
