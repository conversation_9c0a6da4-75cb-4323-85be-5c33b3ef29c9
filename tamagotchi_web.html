<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐾 拓麻歌子 Web版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }
        
        .title {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .pet-display {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .pet-display:hover {
            transform: scale(1.05);
        }
        
        .pet-emoji {
            font-size: 6em;
            margin-bottom: 10px;
            transition: transform 0.5s ease;
        }
        
        .pet-emoji.happy {
            transform: scale(1.2);
        }
        
        .pet-info {
            font-size: 1.2em;
            font-weight: 600;
            color: #555;
        }
        
        .pet-mood {
            font-size: 0.9em;
            color: #888;
            margin-top: 5px;
        }
        
        .stats {
            margin: 20px 0;
        }
        
        .stat-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }
        
        .stat-label {
            font-weight: 600;
            color: #555;
        }
        
        .stat-value {
            font-weight: bold;
            color: #333;
        }
        
        .progress-bar {
            width: 60%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 0 10px;
        }
        
        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }
        
        .hunger { background: #ff9500; }
        .happiness { background: #34c759; }
        .health { background: #ff3b30; }
        
        .actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-btn {
            background: #007aff;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .action-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .action-btn:active {
            transform: translateY(0);
        }
        
        .action-icon {
            font-size: 1.5em;
        }
        
        .hatch-hint {
            color: #888;
            font-style: italic;
            margin-top: 10px;
        }
        
        .stats-btn {
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            margin-top: 20px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🐾 拓麻歌子</h1>
        
        <div class="pet-display" onclick="hatchPet()">
            <div class="pet-emoji" id="petEmoji">🥚</div>
            <div class="pet-info" id="petInfo">蛋</div>
            <div class="pet-mood" id="petMood">等待孵化</div>
        </div>
        
        <div class="stats" id="stats" style="display: none;">
            <div class="stat-bar">
                <span class="stat-label">饥饿度</span>
                <div class="progress-bar">
                    <div class="progress-fill hunger" id="hungerBar"></div>
                </div>
                <span class="stat-value" id="hungerValue">50/100</span>
            </div>
            
            <div class="stat-bar">
                <span class="stat-label">快乐度</span>
                <div class="progress-bar">
                    <div class="progress-fill happiness" id="happinessBar"></div>
                </div>
                <span class="stat-value" id="happinessValue">50/100</span>
            </div>
            
            <div class="stat-bar">
                <span class="stat-label">健康度</span>
                <div class="progress-bar">
                    <div class="progress-fill health" id="healthBar"></div>
                </div>
                <span class="stat-value" id="healthValue">100/100</span>
            </div>
        </div>
        
        <div class="actions" id="actions" style="display: none;">
            <button class="action-btn" onclick="feedPet()">
                <span class="action-icon">🍎</span>
                <span>喂食</span>
            </button>
            
            <button class="action-btn" onclick="playWithPet()">
                <span class="action-icon">🎾</span>
                <span>游戏</span>
            </button>
            
            <button class="action-btn" onclick="healPet()">
                <span class="action-icon">💊</span>
                <span>治疗</span>
            </button>
            
            <button class="action-btn" onclick="growPet()">
                <span class="action-icon">⬆️</span>
                <span>成长</span>
            </button>
        </div>
        
        <div class="hatch-hint" id="hatchHint">点击蛋来孵化你的宠物！</div>
        
        <button class="stats-btn" onclick="showStats()" id="statsBtn" style="display: none;">查看详细统计</button>
    </div>
    
    <div class="notification" id="notification"></div>

    <script>
        // 宠物状态
        let pet = {
            stage: 'egg',
            hunger: 50,
            happiness: 50,
            health: 100,
            age: 0,
            mood: 'normal'
        };
        
        const stages = {
            egg: { emoji: '🥚', name: '蛋' },
            baby: { emoji: '🐣', name: '幼体' },
            child: { emoji: '🐤', name: '儿童' },
            teen: { emoji: '🐦', name: '青少年' },
            adult: { emoji: '🦅', name: '成年' }
        };
        
        const moods = {
            happy: { emoji: '😊', name: '开心' },
            normal: { emoji: '😐', name: '正常' },
            sad: { emoji: '😢', name: '伤心' },
            sick: { emoji: '🤒', name: '生病' },
            sleeping: { emoji: '😴', name: '困倦' }
        };
        
        function updateDisplay() {
            const petEmoji = document.getElementById('petEmoji');
            const petInfo = document.getElementById('petInfo');
            const petMood = document.getElementById('petMood');
            
            petEmoji.textContent = stages[pet.stage].emoji;
            petInfo.textContent = stages[pet.stage].name;
            petMood.textContent = moods[pet.mood].name + ' ' + moods[pet.mood].emoji;
            
            // 更新心情动画
            petEmoji.className = pet.mood === 'happy' ? 'pet-emoji happy' : 'pet-emoji';
            
            if (pet.stage !== 'egg') {
                document.getElementById('stats').style.display = 'block';
                document.getElementById('actions').style.display = 'grid';
                document.getElementById('hatchHint').style.display = 'none';
                document.getElementById('statsBtn').style.display = 'block';
                
                updateStats();
            }
        }
        
        function updateStats() {
            document.getElementById('hungerBar').style.width = pet.hunger + '%';
            document.getElementById('hungerValue').textContent = pet.hunger + '/100';
            
            document.getElementById('happinessBar').style.width = pet.happiness + '%';
            document.getElementById('happinessValue').textContent = pet.happiness + '/100';
            
            document.getElementById('healthBar').style.width = pet.health + '%';
            document.getElementById('healthValue').textContent = pet.health + '/100';
            
            updateMood();
        }
        
        function updateMood() {
            if (pet.health < 30) {
                pet.mood = 'sick';
            } else if (pet.hunger > 80) {
                pet.mood = 'sad';
            } else if (pet.happiness > 70) {
                pet.mood = 'happy';
            } else if (pet.happiness < 30) {
                pet.mood = 'sad';
            } else {
                pet.mood = 'normal';
            }
        }
        
        function hatchPet() {
            if (pet.stage === 'egg') {
                pet.stage = 'baby';
                showNotification('🎉 宠物孵化成功！');
                updateDisplay();
            }
        }
        
        function feedPet() {
            pet.hunger = Math.max(0, pet.hunger - 20);
            pet.happiness = Math.min(100, pet.happiness + 10);
            showNotification('🍎 喂食完成！');
            updateDisplay();
        }
        
        function playWithPet() {
            pet.happiness = Math.min(100, pet.happiness + 20);
            pet.hunger = Math.min(100, pet.hunger + 10);
            showNotification('🎾 游戏愉快！');
            updateDisplay();
        }
        
        function healPet() {
            pet.health = Math.min(100, pet.health + 30);
            showNotification('💊 治疗完成！');
            updateDisplay();
        }
        
        function growPet() {
            pet.age += 1;
            
            if (pet.age > 5 && pet.stage === 'baby') {
                pet.stage = 'child';
                showNotification('🌱 宠物成长为儿童！');
            } else if (pet.age > 10 && pet.stage === 'child') {
                pet.stage = 'teen';
                showNotification('🌿 宠物成长为青少年！');
            } else if (pet.age > 15 && pet.stage === 'teen') {
                pet.stage = 'adult';
                showNotification('🌳 宠物成长为成年！');
            } else {
                showNotification('⬆️ 宠物年龄增长！');
            }
            
            updateDisplay();
        }
        
        function showStats() {
            alert(`宠物详细统计：
年龄：${pet.age} 天
阶段：${stages[pet.stage].name}
心情：${moods[pet.mood].name}
饥饿度：${pet.hunger}/100
快乐度：${pet.happiness}/100
健康度：${pet.health}/100`);
        }
        
        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
        
        // 自动状态衰减（每30秒）
        setInterval(() => {
            if (pet.stage !== 'egg') {
                pet.hunger = Math.min(100, pet.hunger + 5);
                pet.happiness = Math.max(0, pet.happiness - 3);
                pet.health = Math.max(0, pet.health - 1);
                updateDisplay();
            }
        }, 30000);
        
        // 初始化显示
        updateDisplay();
    </script>
</body>
</html>
