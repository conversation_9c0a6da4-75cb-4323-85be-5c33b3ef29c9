import Foundation
import SwiftUI

// 食物类型
enum FoodType: String, CaseIterable, Codable {
    case apple = "apple"
    case bread = "bread"
    case meat = "meat"
    case candy = "candy"
    case medicine = "medicine"
    case water = "water"
    
    var emoji: String {
        switch self {
        case .apple: return "🍎"
        case .bread: return "🍞"
        case .meat: return "🍖"
        case .candy: return "🍭"
        case .medicine: return "💊"
        case .water: return "💧"
        }
    }
    
    var name: String {
        switch self {
        case .apple: return "苹果"
        case .bread: return "面包"
        case .meat: return "肉类"
        case .candy: return "糖果"
        case .medicine: return "药物"
        case .water: return "水"
        }
    }
    
    var description: String {
        switch self {
        case .apple: return "健康的水果，增加健康度"
        case .bread: return "基础食物，减少饥饿度"
        case .meat: return "营养丰富，大幅减少饥饿度"
        case .candy: return "甜食，增加快乐度但可能影响健康"
        case .medicine: return "治疗药物，恢复健康度"
        case .water: return "清水，轻微恢复各项属性"
        }
    }
}

// 食物效果
struct FoodEffect {
    let hungerChange: Int      // 饥饿度变化（负数表示减少饥饿）
    let happinessChange: Int   // 快乐度变化
    let healthChange: Int      // 健康度变化
    let energyChange: Int      // 精力变化
}

// 食物模型
struct Food: Identifiable, Codable {
    let id = UUID()
    let type: FoodType
    let effect: FoodEffect
    
    init(type: FoodType) {
        self.type = type
        self.effect = Food.getEffect(for: type)
    }
    
    static func getEffect(for foodType: FoodType) -> FoodEffect {
        switch foodType {
        case .apple:
            return FoodEffect(hungerChange: -20, happinessChange: 5, healthChange: 10, energyChange: 5)
        case .bread:
            return FoodEffect(hungerChange: -30, happinessChange: 0, healthChange: 0, energyChange: 0)
        case .meat:
            return FoodEffect(hungerChange: -40, happinessChange: 10, healthChange: 5, energyChange: 10)
        case .candy:
            return FoodEffect(hungerChange: -10, happinessChange: 20, healthChange: -5, energyChange: 15)
        case .medicine:
            return FoodEffect(hungerChange: 0, happinessChange: -5, healthChange: 30, energyChange: 0)
        case .water:
            return FoodEffect(hungerChange: -5, happinessChange: 2, healthChange: 5, energyChange: 5)
        }
    }
    
    // 获取所有可用食物
    static func getAllFoods() -> [Food] {
        return FoodType.allCases.map { Food(type: $0) }
    }
    
    // 根据宠物状态推荐食物
    static func getRecommendedFood(for pet: Pet) -> [Food] {
        var recommended: [Food] = []
        
        // 如果饥饿，推荐填饱肚子的食物
        if pet.hunger > 60 {
            recommended.append(Food(type: .meat))
            recommended.append(Food(type: .bread))
        }
        
        // 如果不开心，推荐增加快乐的食物
        if pet.happiness < 40 {
            recommended.append(Food(type: .candy))
            recommended.append(Food(type: .meat))
        }
        
        // 如果生病，推荐药物和健康食物
        if pet.health < 50 {
            recommended.append(Food(type: .medicine))
            recommended.append(Food(type: .apple))
        }
        
        // 如果精力不足，推荐能量食物
        if pet.energy < 40 {
            recommended.append(Food(type: .candy))
            recommended.append(Food(type: .meat))
        }
        
        // 如果没有特殊需求，推荐基础食物
        if recommended.isEmpty {
            recommended.append(Food(type: .apple))
            recommended.append(Food(type: .water))
        }
        
        return Array(Set(recommended.map { $0.type })).map { Food(type: $0) }
    }
}

// 喂食结果
struct FeedingResult {
    let success: Bool
    let message: String
    let effectDescription: String
}

// 喂食管理器
class FeedingManager {
    
    // 喂食宠物
    static func feedPet(_ pet: Pet, with food: Food) -> FeedingResult {
        // 检查是否可以喂食
        guard pet.stage != .egg else {
            return FeedingResult(
                success: false,
                message: "蛋还没有孵化，无法喂食！",
                effectDescription: ""
            )
        }
        
        // 检查是否太饱
        if pet.hunger < 10 && food.effect.hungerChange < 0 {
            return FeedingResult(
                success: false,
                message: "\(pet.name)已经很饱了，不想再吃了！",
                effectDescription: ""
            )
        }
        
        // 应用食物效果
        let oldHunger = pet.hunger
        let oldHappiness = pet.happiness
        let oldHealth = pet.health
        let oldEnergy = pet.energy
        
        pet.hunger = max(0, min(100, pet.hunger + food.effect.hungerChange))
        pet.happiness = max(0, min(100, pet.happiness + food.effect.happinessChange))
        pet.health = max(0, min(100, pet.health + food.effect.healthChange))
        pet.energy = max(0, min(100, pet.energy + food.effect.energyChange))
        
        pet.totalFeedings += 1
        pet.updateMood()
        
        // 生成效果描述
        var effects: [String] = []
        
        if food.effect.hungerChange != 0 {
            let change = oldHunger - pet.hunger
            if change > 0 {
                effects.append("饥饿度减少\(change)")
            } else if change < 0 {
                effects.append("饥饿度增加\(-change)")
            }
        }
        
        if food.effect.happinessChange != 0 {
            let change = pet.happiness - oldHappiness
            if change > 0 {
                effects.append("快乐度增加\(change)")
            } else {
                effects.append("快乐度减少\(-change)")
            }
        }
        
        if food.effect.healthChange != 0 {
            let change = pet.health - oldHealth
            if change > 0 {
                effects.append("健康度增加\(change)")
            } else {
                effects.append("健康度减少\(-change)")
            }
        }
        
        if food.effect.energyChange != 0 {
            let change = pet.energy - oldEnergy
            if change > 0 {
                effects.append("精力增加\(change)")
            } else {
                effects.append("精力减少\(-change)")
            }
        }
        
        let effectDescription = effects.isEmpty ? "没有明显变化" : effects.joined(separator: ", ")
        
        return FeedingResult(
            success: true,
            message: "\(pet.name)吃了\(food.type.name)！",
            effectDescription: effectDescription
        )
    }
}
