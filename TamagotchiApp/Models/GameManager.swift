import Foundation
import SwiftUI

class GameManager: ObservableObject {
    @Published var pet: Pet
    private var timer: Timer?
    
    // 数据持久化
    private let userDefaults = UserDefaults.standard
    private let petDataKey = "TamagotchiPetData"
    
    init() {
        // 尝试加载保存的宠物数据
        if let savedData = userDefaults.data(forKey: petDataKey),
           let decodedPet = try? JSONDecoder().decode(Pet.self, from: savedData) {
            self.pet = decodedPet
            print("成功加载保存的宠物数据")
        } else {
            // 创建新宠物
            self.pet = Pet()
            print("创建新宠物")
        }

        // 验证数据完整性
        validatePetData()

        // 启动游戏循环
        startGameLoop()

        // 立即更新一次状态
        pet.updateStatus()

        // 保存初始状态
        savePet()
    }
    
    deinit {
        stopGameLoop()
    }
    
    // 启动游戏循环
    private func startGameLoop() {
        timer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { _ in
            DispatchQueue.main.async {
                self.pet.updateStatus()
                self.checkLifecycleEvents()
                self.savePet()
            }
        }
    }

    // 检查生命周期事件
    private func checkLifecycleEvents() {
        // 检查是否需要自动进化
        let previousStage = pet.stage
        pet.updateStatus()

        if pet.stage != previousStage {
            // 宠物进化了，可以在这里添加通知或特殊效果
            print("\(pet.name) 进化到了 \(pet.stage.name)!")
        }

        // 检查是否需要特殊照顾
        if pet.health < 20 && pet.mood != .sick {
            print("\(pet.name) 需要紧急照顾!")
        }

        // 检查是否长时间没有互动
        let timeSinceLastUpdate = Date().timeIntervalSince(pet.lastUpdateTime)
        if timeSinceLastUpdate > 3600 * 6 { // 6小时没有更新
            // 宠物可能会变得不开心
            pet.happiness = max(0, pet.happiness - 10)
        }
    }
    
    // 停止游戏循环
    private func stopGameLoop() {
        timer?.invalidate()
        timer = nil
    }
    
    // 保存宠物数据
    func savePet() {
        do {
            let encodedData = try JSONEncoder().encode(pet)
            userDefaults.set(encodedData, forKey: petDataKey)
            userDefaults.set(Date(), forKey: "lastSaveTime")
            print("宠物数据已保存")
        } catch {
            print("保存宠物数据失败: \(error)")
        }
    }

    // 导出宠物数据（用于备份）
    func exportPetData() -> Data? {
        do {
            return try JSONEncoder().encode(pet)
        } catch {
            print("导出宠物数据失败: \(error)")
            return nil
        }
    }

    // 导入宠物数据（用于恢复）
    func importPetData(_ data: Data) -> Bool {
        do {
            let importedPet = try JSONDecoder().decode(Pet.self, from: data)
            self.pet = importedPet
            savePet()
            print("宠物数据导入成功")
            return true
        } catch {
            print("导入宠物数据失败: \(error)")
            return false
        }
    }

    // 检查数据完整性
    private func validatePetData() {
        // 确保属性值在有效范围内
        pet.hunger = max(0, min(100, pet.hunger))
        pet.happiness = max(0, min(100, pet.happiness))
        pet.health = max(0, min(100, pet.health))
        pet.energy = max(0, min(100, pet.energy))
        pet.age = max(0, pet.age)

        // 确保时间数据有效
        if pet.birthTime > Date() {
            pet.birthTime = Date()
        }

        if pet.lastUpdateTime > Date() {
            pet.lastUpdateTime = Date()
        }
    }
    
    // 重置游戏（创建新宠物）
    func resetGame() {
        pet = Pet()
        savePet()
    }
    
    // 孵化宠物（从蛋变为幼体）
    func hatchPet() {
        if pet.stage == .egg {
            pet.age = 1
            pet.updateStatus()
            savePet()
        }
    }
    
    // 喂食
    func feedPet() {
        pet.feed()
        savePet()
    }
    
    // 与宠物玩耍
    func playWithPet() {
        pet.play()
        savePet()
    }
    
    // 让宠物休息
    func petSleep() {
        pet.sleep()
        savePet()
    }
    
    // 治疗宠物
    func healPet() {
        pet.heal()
        savePet()
    }
    
    // 获取宠物状态描述
    func getPetStatusDescription() -> String {
        var status = []
        
        if pet.hunger > 70 {
            status.append("很饿")
        } else if pet.hunger > 40 {
            status.append("有点饿")
        }
        
        if pet.happiness < 30 {
            status.append("不开心")
        } else if pet.happiness > 70 {
            status.append("很开心")
        }
        
        if pet.health < 50 {
            status.append("生病了")
        }
        
        if pet.energy < 30 {
            status.append("很累")
        }
        
        if status.isEmpty {
            return "状态良好"
        } else {
            return status.joined(separator: ", ")
        }
    }
    
    // 获取下一阶段所需时间
    func getTimeToNextStage() -> String {
        let nextStageAge: Int
        
        switch pet.stage {
        case .egg:
            nextStageAge = 1
        case .baby:
            nextStageAge = 24
        case .child:
            nextStageAge = 72
        case .teen:
            nextStageAge = 168
        case .adult:
            nextStageAge = 720
        case .elder:
            return "已达到最高阶段"
        }
        
        let hoursLeft = nextStageAge - pet.age
        if hoursLeft <= 0 {
            return "即将进化"
        } else if hoursLeft < 24 {
            return "\(hoursLeft)小时后进化"
        } else {
            let daysLeft = hoursLeft / 24
            return "\(daysLeft)天后进化"
        }
    }
}
