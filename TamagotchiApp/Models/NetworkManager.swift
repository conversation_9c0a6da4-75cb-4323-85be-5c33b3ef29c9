import Foundation
import Network
import Combine

// 网络连接状态
enum NetworkStatus {
    case disconnected
    case connecting
    case connected
    case error(String)
}

// 消息类型
enum MessageType: String, Codable {
    case petStatus = "pet_status"
    case interaction = "interaction"
    case feeding = "feeding"
    case gift = "gift"
    case visit = "visit"
    case heartbeat = "heartbeat"
}

// 网络消息
struct NetworkMessage: Codable {
    let id: UUID
    let type: MessageType
    let senderId: String
    let receiverId: String?
    let timestamp: Date
    let data: Data
    
    init(type: MessageType, senderId: String, receiverId: String? = nil, data: Data) {
        self.id = UUID()
        self.type = type
        self.senderId = senderId
        self.receiverId = receiverId
        self.timestamp = Date()
        self.data = data
    }
}

// 连接的设备信息
struct ConnectedDevice: Identifiable, Codable {
    let id: String
    let name: String
    let petName: String
    let lastSeen: Date
    var isOnline: Bool
}

// 网络管理器
class NetworkManager: ObservableObject {
    @Published var status: NetworkStatus = .disconnected
    @Published var connectedDevices: [ConnectedDevice] = []
    @Published var receivedMessages: [NetworkMessage] = []
    
    private var listener: NWListener?
    private var connection: NWConnection?
    private var deviceId: String
    private var deviceName: String
    private let port: NWEndpoint.Port = 12345
    
    // 消息处理回调
    var onMessageReceived: ((NetworkMessage) -> Void)?
    var onDeviceConnected: ((ConnectedDevice) -> Void)?
    var onDeviceDisconnected: ((String) -> Void)?
    
    init() {
        self.deviceId = UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
        self.deviceName = UIDevice.current.name
    }
    
    // MARK: - 服务器模式（等待连接）
    
    func startListening() {
        guard status == .disconnected else { return }
        
        do {
            listener = try NWListener(using: .tcp, on: port)
            listener?.stateUpdateHandler = { [weak self] state in
                DispatchQueue.main.async {
                    switch state {
                    case .ready:
                        self?.status = .connected
                        print("服务器已启动，等待连接...")
                    case .failed(let error):
                        self?.status = .error("启动服务器失败: \(error)")
                    case .cancelled:
                        self?.status = .disconnected
                    default:
                        break
                    }
                }
            }
            
            listener?.newConnectionHandler = { [weak self] connection in
                self?.handleNewConnection(connection)
            }
            
            listener?.start(queue: .global())
            status = .connecting
            
        } catch {
            status = .error("无法启动服务器: \(error)")
        }
    }
    
    private func handleNewConnection(_ connection: NWConnection) {
        connection.stateUpdateHandler = { [weak self] state in
            switch state {
            case .ready:
                print("新设备已连接")
                self?.setupReceiveHandler(for: connection)
            case .failed(let error):
                print("连接失败: \(error)")
            case .cancelled:
                print("连接已断开")
            default:
                break
            }
        }
        
        connection.start(queue: .global())
    }
    
    // MARK: - 客户端模式（主动连接）
    
    func connectToDevice(host: String) {
        guard status == .disconnected else { return }
        
        let endpoint = NWEndpoint.hostPort(host: NWEndpoint.Host(host), port: port)
        connection = NWConnection(to: endpoint, using: .tcp)
        
        connection?.stateUpdateHandler = { [weak self] state in
            DispatchQueue.main.async {
                switch state {
                case .ready:
                    self?.status = .connected
                    self?.setupReceiveHandler(for: self?.connection)
                    self?.sendHandshake()
                case .failed(let error):
                    self?.status = .error("连接失败: \(error)")
                case .cancelled:
                    self?.status = .disconnected
                default:
                    break
                }
            }
        }
        
        status = .connecting
        connection?.start(queue: .global())
    }
    
    // MARK: - 消息处理
    
    private func setupReceiveHandler(for connection: NWConnection?) {
        connection?.receive(minimumIncompleteLength: 1, maximumLength: 65536) { [weak self] data, _, isComplete, error in
            if let data = data, !data.isEmpty {
                self?.processReceivedData(data)
            }
            
            if let error = error {
                print("接收数据错误: \(error)")
                return
            }
            
            if !isComplete {
                self?.setupReceiveHandler(for: connection)
            }
        }
    }
    
    private func processReceivedData(_ data: Data) {
        do {
            let message = try JSONDecoder().decode(NetworkMessage.self, from: data)
            DispatchQueue.main.async {
                self.receivedMessages.append(message)
                self.onMessageReceived?(message)
                self.handleMessage(message)
            }
        } catch {
            print("解析消息失败: \(error)")
        }
    }
    
    private func handleMessage(_ message: NetworkMessage) {
        switch message.type {
        case .petStatus:
            handlePetStatusMessage(message)
        case .interaction:
            handleInteractionMessage(message)
        case .feeding:
            handleFeedingMessage(message)
        case .gift:
            handleGiftMessage(message)
        case .visit:
            handleVisitMessage(message)
        case .heartbeat:
            handleHeartbeatMessage(message)
        }
    }
    
    // MARK: - 消息发送
    
    func sendMessage(_ message: NetworkMessage) {
        guard let connection = connection, status == .connected else {
            print("无法发送消息：未连接")
            return
        }
        
        do {
            let data = try JSONEncoder().encode(message)
            connection.send(content: data, completion: .contentProcessed { error in
                if let error = error {
                    print("发送消息失败: \(error)")
                }
            })
        } catch {
            print("编码消息失败: \(error)")
        }
    }
    
    func sendPetStatus(_ pet: Pet) {
        guard let petData = try? JSONEncoder().encode(pet) else { return }
        
        let message = NetworkMessage(
            type: .petStatus,
            senderId: deviceId,
            data: petData
        )
        
        sendMessage(message)
    }
    
    func sendInteraction(_ interaction: Interaction, to deviceId: String) {
        guard let interactionData = try? JSONEncoder().encode(interaction) else { return }
        
        let message = NetworkMessage(
            type: .interaction,
            senderId: self.deviceId,
            receiverId: deviceId,
            data: interactionData
        )
        
        sendMessage(message)
    }
    
    func sendGift(_ food: Food, to deviceId: String) {
        guard let foodData = try? JSONEncoder().encode(food) else { return }
        
        let message = NetworkMessage(
            type: .gift,
            senderId: self.deviceId,
            receiverId: deviceId,
            data: foodData
        )
        
        sendMessage(message)
    }
    
    // MARK: - 消息处理器
    
    private func handlePetStatusMessage(_ message: NetworkMessage) {
        // 处理其他设备的宠物状态更新
        print("收到宠物状态更新来自: \(message.senderId)")
    }
    
    private func handleInteractionMessage(_ message: NetworkMessage) {
        // 处理远程互动
        print("收到远程互动来自: \(message.senderId)")
    }
    
    private func handleFeedingMessage(_ message: NetworkMessage) {
        // 处理远程喂食
        print("收到远程喂食来自: \(message.senderId)")
    }
    
    private func handleGiftMessage(_ message: NetworkMessage) {
        // 处理礼物
        do {
            let food = try JSONDecoder().decode(Food.self, from: message.data)
            print("收到礼物: \(food.type.name) 来自: \(message.senderId)")
            // 这里可以添加到礼物队列或直接应用
        } catch {
            print("解析礼物失败: \(error)")
        }
    }
    
    private func handleVisitMessage(_ message: NetworkMessage) {
        // 处理访问
        print("收到访问来自: \(message.senderId)")
    }
    
    private func handleHeartbeatMessage(_ message: NetworkMessage) {
        // 处理心跳，更新设备在线状态
        updateDeviceStatus(message.senderId, isOnline: true)
    }
    
    // MARK: - 设备管理
    
    private func sendHandshake() {
        let handshakeData = [
            "deviceId": deviceId,
            "deviceName": deviceName,
            "petName": "我的宠物" // 这里应该从实际宠物获取
        ]
        
        guard let data = try? JSONSerialization.data(withJSONObject: handshakeData) else { return }
        
        let message = NetworkMessage(
            type: .heartbeat,
            senderId: deviceId,
            data: data
        )
        
        sendMessage(message)
    }
    
    private func updateDeviceStatus(_ deviceId: String, isOnline: Bool) {
        if let index = connectedDevices.firstIndex(where: { $0.id == deviceId }) {
            connectedDevices[index].isOnline = isOnline
            connectedDevices[index] = ConnectedDevice(
                id: connectedDevices[index].id,
                name: connectedDevices[index].name,
                petName: connectedDevices[index].petName,
                lastSeen: Date(),
                isOnline: isOnline
            )
        }
    }
    
    // MARK: - 连接管理
    
    func disconnect() {
        listener?.cancel()
        connection?.cancel()
        listener = nil
        connection = nil
        status = .disconnected
        connectedDevices.removeAll()
    }
    
    deinit {
        disconnect()
    }
}

// MARK: - 网络发现（Bonjour）

class NetworkDiscovery: ObservableObject {
    @Published var discoveredDevices: [String] = []
    
    private var browser: NWBrowser?
    
    func startDiscovery() {
        let parameters = NWParameters()
        parameters.includePeerToPeer = true
        
        browser = NWBrowser(for: .bonjour(type: "_tamagotchi._tcp", domain: nil), using: parameters)
        
        browser?.stateUpdateHandler = { state in
            switch state {
            case .ready:
                print("开始搜索设备...")
            case .failed(let error):
                print("搜索失败: \(error)")
            default:
                break
            }
        }
        
        browser?.browseResultsChangedHandler = { [weak self] results, changes in
            DispatchQueue.main.async {
                self?.discoveredDevices = results.compactMap { result in
                    if case .service(let name, _, _, _) = result.endpoint {
                        return name
                    }
                    return nil
                }
            }
        }
        
        browser?.start(queue: .global())
    }
    
    func stopDiscovery() {
        browser?.cancel()
        browser = nil
        discoveredDevices.removeAll()
    }
}
