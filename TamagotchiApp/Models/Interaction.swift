import Foundation
import SwiftUI

// 互动类型
enum InteractionType: String, CaseIterable, Codable {
    case pet = "pet"           // 抚摸
    case play = "play"         // 玩耍
    case clean = "clean"       // 清洁
    case exercise = "exercise" // 运动
    case sing = "sing"         // 唱歌
    case dance = "dance"       // 跳舞
    
    var emoji: String {
        switch self {
        case .pet: return "🤗"
        case .play: return "🎾"
        case .clean: return "🧼"
        case .exercise: return "🏃"
        case .sing: return "🎵"
        case .dance: return "💃"
        }
    }
    
    var name: String {
        switch self {
        case .pet: return "抚摸"
        case .play: return "玩耍"
        case .clean: return "清洁"
        case .exercise: return "运动"
        case .sing: return "唱歌"
        case .dance: return "跳舞"
        }
    }
    
    var description: String {
        switch self {
        case .pet: return "轻柔地抚摸宠物，增加快乐度"
        case .play: return "和宠物一起玩耍，大幅增加快乐度但消耗精力"
        case .clean: return "给宠物清洁，增加健康度和快乐度"
        case .exercise: return "带宠物运动，增加健康度但消耗精力"
        case .sing: return "为宠物唱歌，增加快乐度"
        case .dance: return "和宠物一起跳舞，增加快乐度和精力"
        }
    }
    
    var energyCost: Int {
        switch self {
        case .pet: return 0
        case .play: return 20
        case .clean: return 5
        case .exercise: return 25
        case .sing: return 5
        case .dance: return 15
        }
    }
}

// 互动效果
struct InteractionEffect {
    let happinessChange: Int   // 快乐度变化
    let healthChange: Int      // 健康度变化
    let energyChange: Int      // 精力变化（宠物的精力变化）
    let hungerChange: Int      // 饥饿度变化
}

// 互动模型
struct Interaction: Identifiable, Codable {
    let id = UUID()
    let type: InteractionType
    let effect: InteractionEffect
    
    init(type: InteractionType) {
        self.type = type
        self.effect = Interaction.getEffect(for: type)
    }
    
    static func getEffect(for interactionType: InteractionType) -> InteractionEffect {
        switch interactionType {
        case .pet:
            return InteractionEffect(happinessChange: 15, healthChange: 0, energyChange: 5, hungerChange: 0)
        case .play:
            return InteractionEffect(happinessChange: 25, healthChange: 5, energyChange: -15, hungerChange: 5)
        case .clean:
            return InteractionEffect(happinessChange: 10, healthChange: 15, energyChange: 0, hungerChange: 0)
        case .exercise:
            return InteractionEffect(happinessChange: 15, healthChange: 20, energyChange: -20, hungerChange: 10)
        case .sing:
            return InteractionEffect(happinessChange: 20, healthChange: 0, energyChange: 5, hungerChange: 0)
        case .dance:
            return InteractionEffect(happinessChange: 20, healthChange: 5, energyChange: -10, hungerChange: 5)
        }
    }
    
    // 获取所有可用互动
    static func getAllInteractions() -> [Interaction] {
        return InteractionType.allCases.map { Interaction(type: $0) }
    }
    
    // 根据宠物状态推荐互动
    static func getRecommendedInteractions(for pet: Pet) -> [Interaction] {
        var recommended: [Interaction] = []
        
        // 如果不开心，推荐增加快乐的互动
        if pet.happiness < 50 {
            recommended.append(Interaction(type: .pet))
            recommended.append(Interaction(type: .sing))
            if pet.energy > 30 {
                recommended.append(Interaction(type: .play))
                recommended.append(Interaction(type: .dance))
            }
        }
        
        // 如果健康度低，推荐健康相关互动
        if pet.health < 60 {
            recommended.append(Interaction(type: .clean))
            if pet.energy > 40 {
                recommended.append(Interaction(type: .exercise))
            }
        }
        
        // 如果精力充沛，推荐消耗精力的互动
        if pet.energy > 70 {
            recommended.append(Interaction(type: .play))
            recommended.append(Interaction(type: .exercise))
            recommended.append(Interaction(type: .dance))
        }
        
        // 如果没有特殊需求，推荐基础互动
        if recommended.isEmpty {
            recommended.append(Interaction(type: .pet))
            recommended.append(Interaction(type: .sing))
        }
        
        return Array(Set(recommended.map { $0.type })).map { Interaction(type: $0) }
    }
}

// 互动结果
struct InteractionResult {
    let success: Bool
    let message: String
    let effectDescription: String
    let specialMessage: String?
}

// 互动管理器
class InteractionManager {
    
    // 与宠物互动
    static func interactWithPet(_ pet: Pet, interaction: Interaction) -> InteractionResult {
        // 检查是否可以互动
        guard pet.stage != .egg else {
            return InteractionResult(
                success: false,
                message: "蛋还没有孵化，无法互动！",
                effectDescription: "",
                specialMessage: nil
            )
        }
        
        // 检查宠物是否太累
        if pet.energy < interaction.type.energyCost {
            return InteractionResult(
                success: false,
                message: "\(pet.name)太累了，需要休息一下！",
                effectDescription: "",
                specialMessage: nil
            )
        }
        
        // 检查特殊状态
        if pet.mood == .sick && interaction.type != .clean {
            return InteractionResult(
                success: false,
                message: "\(pet.name)生病了，现在只想被照顾！",
                effectDescription: "",
                specialMessage: "试试给它清洁或治疗"
            )
        }
        
        // 应用互动效果
        let oldHappiness = pet.happiness
        let oldHealth = pet.health
        let oldEnergy = pet.energy
        let oldHunger = pet.hunger
        
        pet.happiness = max(0, min(100, pet.happiness + interaction.effect.happinessChange))
        pet.health = max(0, min(100, pet.health + interaction.effect.healthChange))
        pet.energy = max(0, min(100, pet.energy + interaction.effect.energyChange))
        pet.hunger = max(0, min(100, pet.hunger + interaction.effect.hungerChange))
        
        pet.totalInteractions += 1
        pet.updateMood()
        
        // 生成效果描述
        var effects: [String] = []
        
        if interaction.effect.happinessChange != 0 {
            let change = pet.happiness - oldHappiness
            if change > 0 {
                effects.append("快乐度增加\(change)")
            } else {
                effects.append("快乐度减少\(-change)")
            }
        }
        
        if interaction.effect.healthChange != 0 {
            let change = pet.health - oldHealth
            if change > 0 {
                effects.append("健康度增加\(change)")
            } else {
                effects.append("健康度减少\(-change)")
            }
        }
        
        if interaction.effect.energyChange != 0 {
            let change = pet.energy - oldEnergy
            if change > 0 {
                effects.append("精力增加\(change)")
            } else {
                effects.append("精力减少\(-change)")
            }
        }
        
        if interaction.effect.hungerChange != 0 {
            let change = pet.hunger - oldHunger
            if change > 0 {
                effects.append("饥饿度增加\(change)")
            } else {
                effects.append("饥饿度减少\(-change)")
            }
        }
        
        let effectDescription = effects.isEmpty ? "没有明显变化" : effects.joined(separator: ", ")
        
        // 生成特殊消息
        let specialMessage = generateSpecialMessage(for: pet, interaction: interaction)
        
        return InteractionResult(
            success: true,
            message: getInteractionMessage(for: interaction.type, petName: pet.name),
            effectDescription: effectDescription,
            specialMessage: specialMessage
        )
    }
    
    private static func getInteractionMessage(for type: InteractionType, petName: String) -> String {
        switch type {
        case .pet:
            return "\(petName)很享受你的抚摸！"
        case .play:
            return "\(petName)和你玩得很开心！"
        case .clean:
            return "\(petName)变得干净整洁了！"
        case .exercise:
            return "\(petName)和你一起运动，感觉很棒！"
        case .sing:
            return "\(petName)很喜欢你的歌声！"
        case .dance:
            return "\(petName)和你一起跳舞，很兴奋！"
        }
    }
    
    private static func generateSpecialMessage(for pet: Pet, interaction: Interaction) -> String? {
        // 根据宠物状态和互动类型生成特殊消息
        if pet.happiness >= 90 {
            return "🌟 \(pet.name)非常开心，闪闪发光！"
        }
        
        if pet.health >= 95 && interaction.type == .clean {
            return "✨ \(pet.name)现在非常健康！"
        }
        
        if interaction.type == .play && pet.happiness > 80 {
            return "🎉 \(pet.name)玩得忘记了时间！"
        }
        
        if interaction.type == .exercise && pet.health > 85 {
            return "💪 \(pet.name)变得更强壮了！"
        }
        
        return nil
    }
}
