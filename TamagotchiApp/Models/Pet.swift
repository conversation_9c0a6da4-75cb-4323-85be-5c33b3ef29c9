import Foundation
import SwiftUI

// 宠物的生命阶段
enum PetStage: String, CaseIterable, Codable {
    case egg = "egg"           // 蛋
    case baby = "baby"         // 幼体
    case child = "child"       // 儿童
    case teen = "teen"         // 青少年
    case adult = "adult"       // 成年
    case elder = "elder"       // 老年
    
    var emoji: String {
        switch self {
        case .egg: return "🥚"
        case .baby: return "🐣"
        case .child: return "🐤"
        case .teen: return "🐥"
        case .adult: return "🐦"
        case .elder: return "🦅"
        }
    }
    
    var name: String {
        switch self {
        case .egg: return "蛋"
        case .baby: return "幼体"
        case .child: return "儿童"
        case .teen: return "青少年"
        case .adult: return "成年"
        case .elder: return "老年"
        }
    }
}

// 宠物的情绪状态
enum PetMood: String, CaseIterable, Codable {
    case happy = "happy"
    case normal = "normal"
    case sad = "sad"
    case sick = "sick"
    case sleeping = "sleeping"
    
    var emoji: String {
        switch self {
        case .happy: return "😊"
        case .normal: return "😐"
        case .sad: return "😢"
        case .sick: return "🤒"
        case .sleeping: return "😴"
        }
    }
}

// 宠物模型
class Pet: ObservableObject, Codable {
    @Published var name: String
    @Published var stage: PetStage
    @Published var mood: PetMood
    
    // 基础属性 (0-100)
    @Published var hunger: Int      // 饥饿度 (0=饱, 100=饿)
    @Published var happiness: Int   // 快乐度 (0=不快乐, 100=很快乐)
    @Published var health: Int      // 健康度 (0=生病, 100=健康)
    @Published var energy: Int      // 精力 (0=疲惫, 100=精力充沛)
    
    // 时间相关
    @Published var age: Int         // 年龄（小时）
    @Published var birthTime: Date
    @Published var lastUpdateTime: Date
    
    // 统计数据
    @Published var totalFeedings: Int
    @Published var totalInteractions: Int
    
    init(name: String = "我的宠物") {
        self.name = name
        self.stage = .egg
        self.mood = .normal
        self.hunger = 50
        self.happiness = 50
        self.health = 100
        self.energy = 100
        self.age = 0
        self.birthTime = Date()
        self.lastUpdateTime = Date()
        self.totalFeedings = 0
        self.totalInteractions = 0
    }
    
    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case name, stage, mood, hunger, happiness, health, energy
        case age, birthTime, lastUpdateTime, totalFeedings, totalInteractions
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        stage = try container.decode(PetStage.self, forKey: .stage)
        mood = try container.decode(PetMood.self, forKey: .mood)
        hunger = try container.decode(Int.self, forKey: .hunger)
        happiness = try container.decode(Int.self, forKey: .happiness)
        health = try container.decode(Int.self, forKey: .health)
        energy = try container.decode(Int.self, forKey: .energy)
        age = try container.decode(Int.self, forKey: .age)
        birthTime = try container.decode(Date.self, forKey: .birthTime)
        lastUpdateTime = try container.decode(Date.self, forKey: .lastUpdateTime)
        totalFeedings = try container.decode(Int.self, forKey: .totalFeedings)
        totalInteractions = try container.decode(Int.self, forKey: .totalInteractions)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(stage, forKey: .stage)
        try container.encode(mood, forKey: .mood)
        try container.encode(hunger, forKey: .hunger)
        try container.encode(happiness, forKey: .happiness)
        try container.encode(health, forKey: .health)
        try container.encode(energy, forKey: .energy)
        try container.encode(age, forKey: .age)
        try container.encode(birthTime, forKey: .birthTime)
        try container.encode(lastUpdateTime, forKey: .lastUpdateTime)
        try container.encode(totalFeedings, forKey: .totalFeedings)
        try container.encode(totalInteractions, forKey: .totalInteractions)
    }
    
    // MARK: - Game Logic
    
    // 更新宠物状态（基于时间流逝）
    func updateStatus() {
        let now = Date()
        let timePassed = now.timeIntervalSince(lastUpdateTime)
        let hoursPassed = Int(timePassed / 3600) // 转换为小时
        
        if hoursPassed > 0 {
            // 增加年龄
            age += hoursPassed
            
            // 随时间降低属性
            hunger = min(100, hunger + hoursPassed * 2)  // 每小时增加2点饥饿
            happiness = max(0, happiness - hoursPassed)   // 每小时减少1点快乐
            energy = max(0, energy - hoursPassed * 3)     // 每小时减少3点精力
            
            // 如果饥饿或不快乐，影响健康
            if hunger > 80 || happiness < 20 {
                health = max(0, health - hoursPassed)
            }
            
            // 更新成长阶段
            updateStage()
            
            // 更新情绪
            updateMood()
            
            lastUpdateTime = now
        }
    }
    
    // 更新成长阶段
    private func updateStage() {
        let newStage: PetStage
        
        switch age {
        case 0..<1:
            newStage = .egg
        case 1..<24:
            newStage = .baby
        case 24..<72:
            newStage = .child
        case 72..<168:
            newStage = .teen
        case 168..<720:
            newStage = .adult
        default:
            newStage = .elder
        }
        
        if newStage != stage {
            stage = newStage
        }
    }
    
    // 更新情绪
    func updateMood() {
        if health < 30 {
            mood = .sick
        } else if energy < 20 {
            mood = .sleeping
        } else if happiness > 70 && hunger < 30 {
            mood = .happy
        } else if happiness < 30 || hunger > 70 {
            mood = .sad
        } else {
            mood = .normal
        }
    }
    
    // 喂食
    func feed() {
        hunger = max(0, hunger - 30)
        happiness = min(100, happiness + 10)
        totalFeedings += 1
        updateMood()
    }
    
    // 互动/玩耍
    func play() {
        happiness = min(100, happiness + 20)
        energy = max(0, energy - 15)
        totalInteractions += 1
        updateMood()
    }
    
    // 休息/睡觉
    func sleep() {
        energy = min(100, energy + 40)
        happiness = min(100, happiness + 5)
        updateMood()
    }
    
    // 治疗
    func heal() {
        health = min(100, health + 30)
        happiness = min(100, happiness + 10)
        updateMood()
    }
}
