import SwiftUI

struct ContentView: View {
    @StateObject private var gameManager = GameManager()
    @State private var showingStats = false
    @State private var showingFeeding = false
    @State private var showingInteraction = false
    @State private var showingSettings = false
    @State private var showingConnectivity = false

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 宠物显示区域
                PetView(pet: gameManager.pet) {
                    if gameManager.pet.stage == .egg {
                        gameManager.hatchPet()
                    } else {
                        gameManager.playWithPet()
                    }
                }

                // 状态描述
                Text(gameManager.getPetStatusDescription())
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                // 状态条
                VStack(spacing: 10) {
                    StatusBar(title: "饥饿", value: 100 - gameManager.pet.hunger, color: .green)
                    StatusBar(title: "快乐", value: gameManager.pet.happiness, color: .yellow)
                    StatusBar(title: "健康", value: gameManager.pet.health, color: .red)
                    StatusBar(title: "精力", value: gameManager.pet.energy, color: .blue)
                }
                .padding(.horizontal)

                // 操作按钮
                if gameManager.pet.stage != .egg {
                    VStack(spacing: 15) {
                        HStack(spacing: 15) {
                            ActionButton(title: "喂食", icon: "🍎") {
                                showingFeeding = true
                            }

                            ActionButton(title: "互动", icon: "🎾") {
                                showingInteraction = true
                            }
                        }

                        HStack(spacing: 15) {
                            ActionButton(title: "休息", icon: "😴") {
                                gameManager.petSleep()
                            }

                            ActionButton(title: "连接", icon: "📱") {
                                showingConnectivity = true
                            }
                        }
                    }
                    .padding(.horizontal)
                } else {
                    Text("点击蛋来孵化你的宠物！")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .padding()
                }

                Spacer()
            }
            .padding()
            .navigationTitle("拓麻歌子")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("设置") {
                        showingSettings = true
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("统计") {
                        showingStats = true
                    }
                }
            }
            .sheet(isPresented: $showingStats) {
                StatsView(pet: gameManager.pet, gameManager: gameManager)
            }
            .sheet(isPresented: $showingFeeding) {
                FeedingView(pet: gameManager.pet, gameManager: gameManager)
            }
            .sheet(isPresented: $showingInteraction) {
                InteractionView(pet: gameManager.pet, gameManager: gameManager)
            }
            .sheet(isPresented: $showingSettings) {
                SettingsView(pet: gameManager.pet, gameManager: gameManager)
            }
            .sheet(isPresented: $showingConnectivity) {
                ConnectivityView()
            }
        }
    }
}

struct StatusBar: View {
    let title: String
    let value: Int
    let color: Color

    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .frame(width: 40, alignment: .leading)

            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 8)
                        .cornerRadius(4)

                    Rectangle()
                        .fill(color)
                        .frame(width: geometry.size.width * CGFloat(value) / 100, height: 8)
                        .cornerRadius(4)
                        .animation(.easeInOut(duration: 0.3), value: value)
                }
            }
            .frame(height: 8)

            Text("\(value)")
                .font(.caption)
                .frame(width: 30, alignment: .trailing)
        }
    }
}

struct ActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack {
                Text(icon)
                    .font(.title2)
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.systemBlue))
            .foregroundColor(.white)
            .cornerRadius(10)
        }
    }
}

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
    }
}
