import SwiftUI

struct ConnectivityView: View {
    @StateObject private var networkManager = NetworkManager()
    @StateObject private var networkDiscovery = NetworkDiscovery()
    @Environment(\.dismiss) private var dismiss
    
    @State private var isHosting = false
    @State private var hostIP = ""
    @State private var showingConnectionAlert = false
    @State private var connectionMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 连接状态
                VStack {
                    StatusIndicator(status: networkManager.status)
                    
                    Text(statusDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(15)
                
                // 连接选项
                VStack(spacing: 15) {
                    Text("连接选项")
                        .font(.headline)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    // 主机模式
                    VStack(alignment: .leading, spacing: 10) {
                        HStack {
                            Image(systemName: "wifi.router")
                                .foregroundColor(.blue)
                            Text("作为主机")
                                .fontWeight(.medium)
                            Spacer()
                            Toggle("", isOn: $isHosting)
                                .onChange(of: isHosting) { newValue in
                                    if newValue {
                                        networkManager.startListening()
                                    } else {
                                        networkManager.disconnect()
                                    }
                                }
                        }
                        
                        Text("其他设备可以连接到你的宠物")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                    
                    // 客户端模式
                    VStack(alignment: .leading, spacing: 10) {
                        HStack {
                            Image(systemName: "network")
                                .foregroundColor(.green)
                            Text("连接到其他设备")
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            TextField("输入IP地址", text: $hostIP)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                            
                            Button("连接") {
                                connectToHost()
                            }
                            .disabled(hostIP.isEmpty || networkManager.status == .connecting)
                        }
                        
                        Text("输入要连接的设备IP地址")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
                
                // 发现的设备
                if !networkDiscovery.discoveredDevices.isEmpty {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("发现的设备")
                            .font(.headline)
                        
                        ForEach(networkDiscovery.discoveredDevices, id: \.self) { device in
                            HStack {
                                Image(systemName: "iphone")
                                    .foregroundColor(.blue)
                                Text(device)
                                Spacer()
                                Button("连接") {
                                    // 连接到发现的设备
                                }
                                .buttonStyle(.bordered)
                                .controlSize(.small)
                            }
                            .padding(.vertical, 5)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
                
                // 已连接的设备
                if !networkManager.connectedDevices.isEmpty {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("已连接的设备")
                            .font(.headline)
                        
                        ForEach(networkManager.connectedDevices) { device in
                            ConnectedDeviceRow(device: device, networkManager: networkManager)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                }
                
                Spacer()
                
                // 功能说明
                VStack(alignment: .leading, spacing: 8) {
                    Text("连接功能")
                        .font(.headline)
                    
                    FeatureRow(icon: "heart.fill", title: "互动", description: "与朋友的宠物互动")
                    FeatureRow(icon: "gift.fill", title: "礼物", description: "发送食物给朋友")
                    FeatureRow(icon: "eye.fill", title: "访问", description: "查看朋友的宠物状态")
                    FeatureRow(icon: "bell.fill", title: "通知", description: "接收朋友的消息")
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
            }
            .padding()
            .navigationTitle("连接")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("搜索") {
                        if networkDiscovery.discoveredDevices.isEmpty {
                            networkDiscovery.startDiscovery()
                        } else {
                            networkDiscovery.stopDiscovery()
                        }
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .alert("连接状态", isPresented: $showingConnectionAlert) {
                Button("确定") { }
            } message: {
                Text(connectionMessage)
            }
            .onReceive(networkManager.$status) { status in
                handleStatusChange(status)
            }
        }
    }
    
    private var statusDescription: String {
        switch networkManager.status {
        case .disconnected:
            return "未连接\n选择连接模式开始"
        case .connecting:
            return "连接中...\n请稍候"
        case .connected:
            return "已连接\n可以与朋友互动了"
        case .error(let message):
            return "连接错误\n\(message)"
        }
    }
    
    private func connectToHost() {
        networkManager.connectToDevice(host: hostIP)
    }
    
    private func handleStatusChange(_ status: NetworkStatus) {
        switch status {
        case .connected:
            connectionMessage = "连接成功！现在可以与朋友的宠物互动了。"
            showingConnectionAlert = true
        case .error(let message):
            connectionMessage = "连接失败：\(message)"
            showingConnectionAlert = true
        default:
            break
        }
    }
}

struct StatusIndicator: View {
    let status: NetworkStatus
    
    var body: some View {
        HStack {
            Circle()
                .fill(statusColor)
                .frame(width: 12, height: 12)
            
            Text(statusText)
                .font(.headline)
                .fontWeight(.medium)
        }
    }
    
    private var statusColor: Color {
        switch status {
        case .disconnected:
            return .gray
        case .connecting:
            return .orange
        case .connected:
            return .green
        case .error:
            return .red
        }
    }
    
    private var statusText: String {
        switch status {
        case .disconnected:
            return "未连接"
        case .connecting:
            return "连接中"
        case .connected:
            return "已连接"
        case .error:
            return "连接错误"
        }
    }
}

struct ConnectedDeviceRow: View {
    let device: ConnectedDevice
    let networkManager: NetworkManager
    
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text(device.name)
                    .fontWeight(.medium)
                Text(device.petName)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            HStack(spacing: 10) {
                Circle()
                    .fill(device.isOnline ? .green : .gray)
                    .frame(width: 8, height: 8)
                
                Menu {
                    Button("发送礼物") {
                        // 发送礼物功能
                    }
                    
                    Button("远程互动") {
                        // 远程互动功能
                    }
                    
                    Button("查看宠物") {
                        // 查看朋友宠物功能
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(.vertical, 5)
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            VStack(alignment: .leading) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

struct ConnectivityView_Previews: PreviewProvider {
    static var previews: some View {
        ConnectivityView()
    }
}
