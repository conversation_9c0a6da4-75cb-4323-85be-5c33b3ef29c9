import SwiftUI

struct InteractionView: View {
    @ObservedObject var pet: Pet
    let gameManager: GameManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedInteraction: Interaction?
    @State private var showingInteractionResult = false
    @State private var interactionResult: InteractionResult?
    @State private var showingRecommended = true
    @State private var isInteracting = false
    
    private let allInteractions = Interaction.getAllInteractions()
    private var recommendedInteractions: [Interaction] {
        Interaction.getRecommendedInteractions(for: pet)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 宠物状态显示
                VStack {
                    Text(pet.stage.emoji)
                        .font(.system(size: 80))
                        .scaleEffect(isInteracting ? 1.2 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isInteracting)
                    
                    Text(pet.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("情绪: \(pet.mood.emoji)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if pet.stage == .egg {
                        Text("蛋还没有孵化，无法互动")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        HStack(spacing: 20) {
                            StatusMini(title: "快乐", value: pet.happiness, color: .yellow)
                            StatusMini(title: "健康", value: pet.health, color: .green)
                            StatusMini(title: "精力", value: pet.energy, color: .blue)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(15)
                
                if pet.stage != .egg {
                    // 互动选择区域
                    VStack(alignment: .leading, spacing: 15) {
                        // 切换按钮
                        Picker("互动类型", selection: $showingRecommended) {
                            Text("推荐").tag(true)
                            Text("全部").tag(false)
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        
                        // 互动网格
                        let interactions = showingRecommended ? recommendedInteractions : allInteractions
                        
                        if interactions.isEmpty {
                            Text("没有推荐的互动")
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding()
                        } else {
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 15) {
                                ForEach(interactions, id: \.type) { interaction in
                                    InteractionCard(
                                        interaction: interaction,
                                        isSelected: selectedInteraction?.type == interaction.type,
                                        canPerform: canPerformInteraction(interaction)
                                    ) {
                                        selectedInteraction = interaction
                                    }
                                }
                            }
                        }
                        
                        // 选中互动的详细信息
                        if let selectedInteraction = selectedInteraction {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("互动详情")
                                    .font(.headline)
                                
                                HStack {
                                    Text(selectedInteraction.type.emoji)
                                        .font(.title2)
                                    VStack(alignment: .leading) {
                                        Text(selectedInteraction.type.name)
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        Text(selectedInteraction.type.description)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    Spacer()
                                }
                                
                                // 精力消耗
                                if selectedInteraction.type.energyCost > 0 {
                                    HStack {
                                        Image(systemName: "bolt.fill")
                                            .foregroundColor(.yellow)
                                        Text("消耗精力: \(selectedInteraction.type.energyCost)")
                                            .font(.caption)
                                    }
                                }
                                
                                // 效果预览
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("效果:")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                    
                                    HStack {
                                        if selectedInteraction.effect.happinessChange != 0 {
                                            EffectChip(
                                                title: "快乐",
                                                value: selectedInteraction.effect.happinessChange,
                                                color: .yellow
                                            )
                                        }
                                        
                                        if selectedInteraction.effect.healthChange != 0 {
                                            EffectChip(
                                                title: "健康",
                                                value: selectedInteraction.effect.healthChange,
                                                color: .green
                                            )
                                        }
                                        
                                        if selectedInteraction.effect.energyChange != 0 {
                                            EffectChip(
                                                title: "精力",
                                                value: selectedInteraction.effect.energyChange,
                                                color: .blue
                                            )
                                        }
                                        
                                        if selectedInteraction.effect.hungerChange != 0 {
                                            EffectChip(
                                                title: "饥饿",
                                                value: selectedInteraction.effect.hungerChange,
                                                color: .red,
                                                isReversed: true
                                            )
                                        }
                                    }
                                }
                                
                                // 警告信息
                                if !canPerformInteraction(selectedInteraction) {
                                    HStack {
                                        Image(systemName: "exclamationmark.triangle.fill")
                                            .foregroundColor(.orange)
                                        Text(getWarningMessage(for: selectedInteraction))
                                            .font(.caption)
                                            .foregroundColor(.orange)
                                    }
                                }
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(10)
                        }
                        
                        Spacer()
                        
                        // 互动按钮
                        Button(action: performInteraction) {
                            HStack {
                                Image(systemName: "heart.fill")
                                Text("开始互动")
                                    .fontWeight(.medium)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(canPerformSelectedInteraction() ? Color.blue : Color.gray)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(!canPerformSelectedInteraction())
                    }
                    .padding(.horizontal)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("互动")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .alert("互动结果", isPresented: $showingInteractionResult) {
                Button("确定") { }
            } message: {
                if let result = interactionResult {
                    VStack {
                        Text(result.message)
                        if !result.effectDescription.isEmpty {
                            Text(result.effectDescription)
                        }
                        if let specialMessage = result.specialMessage {
                            Text(specialMessage)
                        }
                    }
                }
            }
        }
    }
    
    private func canPerformInteraction(_ interaction: Interaction) -> Bool {
        if pet.stage == .egg { return false }
        if pet.energy < interaction.type.energyCost { return false }
        if pet.mood == .sick && interaction.type != .clean { return false }
        return true
    }
    
    private func canPerformSelectedInteraction() -> Bool {
        guard let interaction = selectedInteraction else { return false }
        return canPerformInteraction(interaction)
    }
    
    private func getWarningMessage(for interaction: Interaction) -> String {
        if pet.energy < interaction.type.energyCost {
            return "精力不足，需要休息"
        }
        if pet.mood == .sick && interaction.type != .clean {
            return "宠物生病了，需要清洁或治疗"
        }
        return ""
    }
    
    private func performInteraction() {
        guard let interaction = selectedInteraction else { return }
        
        // 播放互动动画
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            isInteracting = true
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            isInteracting = false
            
            let result = InteractionManager.interactWithPet(pet, interaction: interaction)
            interactionResult = result
            showingInteractionResult = true
            
            if result.success {
                gameManager.savePet()
                selectedInteraction = nil
            }
        }
    }
}

struct InteractionCard: View {
    let interaction: Interaction
    let isSelected: Bool
    let canPerform: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(interaction.type.emoji)
                    .font(.title)
                    .opacity(canPerform ? 1.0 : 0.5)
                
                Text(interaction.type.name)
                    .font(.caption)
                    .fontWeight(.medium)
                    .opacity(canPerform ? 1.0 : 0.5)
                
                if interaction.type.energyCost > 0 {
                    HStack(spacing: 2) {
                        Image(systemName: "bolt.fill")
                            .font(.caption2)
                        Text("\(interaction.type.energyCost)")
                            .font(.caption2)
                    }
                    .foregroundColor(.yellow)
                    .opacity(canPerform ? 1.0 : 0.5)
                }
            }
            .frame(width: 80, height: 90)
            .background(
                Group {
                    if !canPerform {
                        Color(.systemGray5)
                    } else if isSelected {
                        Color.blue.opacity(0.2)
                    } else {
                        Color(.systemGray6)
                    }
                }
            )
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(
                        isSelected && canPerform ? Color.blue : Color.clear,
                        lineWidth: 2
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(!canPerform)
    }
}

struct InteractionView_Previews: PreviewProvider {
    static var previews: some View {
        InteractionView(pet: Pet(), gameManager: GameManager())
    }
}
