import SwiftUI

struct SettingsView: View {
    @ObservedObject var pet: Pet
    let gameManager: GameManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var petName: String = ""
    @State private var showingResetAlert = false
    @State private var showingExportAlert = false
    @State private var showingImportAlert = false
    @State private var exportData: Data?
    @State private var notificationsEnabled = true
    @State private var soundEnabled = true
    @State private var autoSaveEnabled = true
    
    var body: some View {
        NavigationView {
            List {
                Section("宠物设置") {
                    HStack {
                        Text("宠物名字")
                        Spacer()
                        TextField("输入名字", text: $petName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(width: 120)
                    }
                    
                    Button("保存名字") {
                        if !petName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            pet.name = petName
                            gameManager.savePet()
                        }
                    }
                    .disabled(petName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                
                Section("应用设置") {
                    Toggle("推送通知", isOn: $notificationsEnabled)
                    Toggle("音效", isOn: $soundEnabled)
                    Toggle("自动保存", isOn: $autoSaveEnabled)
                }
                
                Section("数据管理") {
                    Button("导出宠物数据") {
                        exportPetData()
                    }
                    
                    Button("导入宠物数据") {
                        showingImportAlert = true
                    }
                    
                    Button("备份到iCloud") {
                        // 这里可以实现iCloud备份功能
                    }
                    .disabled(true) // 暂时禁用
                    .foregroundColor(.gray)
                }
                
                Section("游戏信息") {
                    HStack {
                        Text("版本")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("宠物年龄")
                        Spacer()
                        Text("\(pet.age) 小时")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("总喂食次数")
                        Spacer()
                        Text("\(pet.totalFeedings)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("总互动次数")
                        Spacer()
                        Text("\(pet.totalInteractions)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("存活时间")
                        Spacer()
                        Text(getLifeTimeDescription())
                            .foregroundColor(.secondary)
                    }
                }
                
                Section("高级选项") {
                    Button("重置游戏") {
                        showingResetAlert = true
                    }
                    .foregroundColor(.red)
                }
                
                Section("帮助与支持") {
                    Button("游戏说明") {
                        // 可以打开游戏说明页面
                    }
                    
                    Button("联系我们") {
                        // 可以打开邮件或反馈页面
                    }
                    
                    Button("评价应用") {
                        // 可以跳转到App Store评价
                    }
                }
            }
            .navigationTitle("设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                petName = pet.name
                loadSettings()
            }
            .alert("重置游戏", isPresented: $showingResetAlert) {
                Button("取消", role: .cancel) { }
                Button("重置", role: .destructive) {
                    gameManager.resetGame()
                    dismiss()
                }
            } message: {
                Text("这将删除当前宠物并创建一个新的。此操作无法撤销。")
            }
            .alert("导出成功", isPresented: $showingExportAlert) {
                Button("确定") { }
            } message: {
                Text("宠物数据已复制到剪贴板，您可以将其保存到安全的地方。")
            }
            .alert("导入宠物数据", isPresented: $showingImportAlert) {
                Button("取消", role: .cancel) { }
                Button("从剪贴板导入") {
                    importFromClipboard()
                }
            } message: {
                Text("这将用剪贴板中的数据替换当前宠物。请确保数据来源可靠。")
            }
        }
    }
    
    private func getLifeTimeDescription() -> String {
        let timeAlive = Date().timeIntervalSince(pet.birthTime)
        let days = Int(timeAlive) / 86400
        let hours = (Int(timeAlive) % 86400) / 3600
        let minutes = (Int(timeAlive) % 3600) / 60
        
        if days > 0 {
            return "\(days)天 \(hours)小时"
        } else if hours > 0 {
            return "\(hours)小时 \(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
    
    private func loadSettings() {
        let userDefaults = UserDefaults.standard
        notificationsEnabled = userDefaults.bool(forKey: "notificationsEnabled")
        soundEnabled = userDefaults.bool(forKey: "soundEnabled")
        autoSaveEnabled = userDefaults.bool(forKey: "autoSaveEnabled")
    }
    
    private func saveSettings() {
        let userDefaults = UserDefaults.standard
        userDefaults.set(notificationsEnabled, forKey: "notificationsEnabled")
        userDefaults.set(soundEnabled, forKey: "soundEnabled")
        userDefaults.set(autoSaveEnabled, forKey: "autoSaveEnabled")
    }
    
    private func exportPetData() {
        guard let data = gameManager.exportPetData() else {
            return
        }
        
        let base64String = data.base64EncodedString()
        UIPasteboard.general.string = base64String
        showingExportAlert = true
    }
    
    private func importFromClipboard() {
        guard let clipboardString = UIPasteboard.general.string,
              let data = Data(base64Encoded: clipboardString) else {
            return
        }
        
        if gameManager.importPetData(data) {
            dismiss()
        }
    }
}

struct GameGuideView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("游戏说明")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding(.bottom)
                    
                    GuideSection(
                        title: "基础玩法",
                        content: "拓麻歌子是一个虚拟宠物游戏。你需要照顾你的宠物，喂食、互动、清洁，让它健康快乐地成长。"
                    )
                    
                    GuideSection(
                        title: "宠物属性",
                        content: """
                        • 饥饿度：宠物的饥饿程度，需要定期喂食
                        • 快乐度：宠物的心情，通过互动提升
                        • 健康度：宠物的健康状况，影响整体状态
                        • 精力：宠物的体力，休息可以恢复
                        """
                    )
                    
                    GuideSection(
                        title: "成长阶段",
                        content: """
                        宠物会随着时间成长：
                        蛋 → 幼体 → 儿童 → 青少年 → 成年 → 老年
                        
                        每个阶段都有不同的特点和需求。
                        """
                    )
                    
                    GuideSection(
                        title: "照顾技巧",
                        content: """
                        • 定期检查宠物状态
                        • 根据推荐选择合适的食物和互动
                        • 注意宠物的情绪变化
                        • 保持各项属性的平衡
                        """
                    )
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("游戏指南")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct GuideSection: View {
    let title: String
    let content: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(content)
                .font(.body)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}

struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SettingsView(pet: Pet(), gameManager: GameManager())
    }
}
