import SwiftUI

struct FeedingView: View {
    @ObservedObject var pet: Pet
    let gameManager: GameManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedFood: Food?
    @State private var showingFeedingResult = false
    @State private var feedingResult: FeedingResult?
    @State private var showingRecommended = true
    
    private let allFoods = Food.getAllFoods()
    private var recommendedFoods: [Food] {
        Food.getRecommendedFood(for: pet)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 宠物状态显示
                VStack {
                    Text(pet.stage.emoji)
                        .font(.system(size: 80))
                    
                    Text(pet.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    if pet.stage == .egg {
                        Text("蛋还没有孵化，无法喂食")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        HStack(spacing: 20) {
                            StatusMini(title: "饥饿", value: pet.hunger, color: .red, isReversed: true)
                            StatusMini(title: "快乐", value: pet.happiness, color: .yellow)
                            StatusMini(title: "健康", value: pet.health, color: .green)
                            StatusMini(title: "精力", value: pet.energy, color: .blue)
                        }
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(15)
                
                if pet.stage != .egg {
                    // 食物选择区域
                    VStack(alignment: .leading, spacing: 15) {
                        // 切换按钮
                        Picker("食物类型", selection: $showingRecommended) {
                            Text("推荐").tag(true)
                            Text("全部").tag(false)
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        
                        // 食物网格
                        let foods = showingRecommended ? recommendedFoods : allFoods
                        
                        if foods.isEmpty {
                            Text("没有推荐的食物")
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding()
                        } else {
                            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 15) {
                                ForEach(foods, id: \.type) { food in
                                    FoodCard(
                                        food: food,
                                        isSelected: selectedFood?.type == food.type
                                    ) {
                                        selectedFood = food
                                    }
                                }
                            }
                        }
                        
                        // 选中食物的详细信息
                        if let selectedFood = selectedFood {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("食物详情")
                                    .font(.headline)
                                
                                HStack {
                                    Text(selectedFood.type.emoji)
                                        .font(.title2)
                                    VStack(alignment: .leading) {
                                        Text(selectedFood.type.name)
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        Text(selectedFood.type.description)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    Spacer()
                                }
                                
                                // 效果预览
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("效果:")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                    
                                    HStack {
                                        if selectedFood.effect.hungerChange != 0 {
                                            EffectChip(
                                                title: "饥饿",
                                                value: selectedFood.effect.hungerChange,
                                                color: .red,
                                                isReversed: true
                                            )
                                        }
                                        
                                        if selectedFood.effect.happinessChange != 0 {
                                            EffectChip(
                                                title: "快乐",
                                                value: selectedFood.effect.happinessChange,
                                                color: .yellow
                                            )
                                        }
                                        
                                        if selectedFood.effect.healthChange != 0 {
                                            EffectChip(
                                                title: "健康",
                                                value: selectedFood.effect.healthChange,
                                                color: .green
                                            )
                                        }
                                        
                                        if selectedFood.effect.energyChange != 0 {
                                            EffectChip(
                                                title: "精力",
                                                value: selectedFood.effect.energyChange,
                                                color: .blue
                                            )
                                        }
                                    }
                                }
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(10)
                        }
                        
                        Spacer()
                        
                        // 喂食按钮
                        Button(action: feedPet) {
                            HStack {
                                Image(systemName: "heart.fill")
                                Text("喂食")
                                    .fontWeight(.medium)
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(selectedFood != nil ? Color.blue : Color.gray)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                        }
                        .disabled(selectedFood == nil)
                    }
                    .padding(.horizontal)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("喂食")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .alert("喂食结果", isPresented: $showingFeedingResult) {
                Button("确定") { }
            } message: {
                if let result = feedingResult {
                    VStack {
                        Text(result.message)
                        if !result.effectDescription.isEmpty {
                            Text(result.effectDescription)
                        }
                    }
                }
            }
        }
    }
    
    private func feedPet() {
        guard let food = selectedFood else { return }
        
        let result = FeedingManager.feedPet(pet, with: food)
        feedingResult = result
        showingFeedingResult = true
        
        if result.success {
            gameManager.savePet()
            selectedFood = nil
        }
    }
}

struct FoodCard: View {
    let food: Food
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Text(food.type.emoji)
                    .font(.title)
                
                Text(food.type.name)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .frame(width: 80, height: 80)
            .background(isSelected ? Color.blue.opacity(0.2) : Color(.systemGray6))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct StatusMini: View {
    let title: String
    let value: Int
    let color: Color
    let isReversed: Bool
    
    init(title: String, value: Int, color: Color, isReversed: Bool = false) {
        self.title = title
        self.value = value
        self.color = color
        self.isReversed = isReversed
    }
    
    var body: some View {
        VStack(spacing: 2) {
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Text("\(value)")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(getStatusColor())
        }
    }
    
    private func getStatusColor() -> Color {
        let adjustedValue = isReversed ? 100 - value : value
        
        if adjustedValue > 70 {
            return .green
        } else if adjustedValue > 40 {
            return .yellow
        } else {
            return .red
        }
    }
}

struct EffectChip: View {
    let title: String
    let value: Int
    let color: Color
    let isReversed: Bool
    
    init(title: String, value: Int, color: Color, isReversed: Bool = false) {
        self.title = title
        self.value = value
        self.color = color
        self.isReversed = isReversed
    }
    
    var body: some View {
        HStack(spacing: 2) {
            Text(title)
            Text(getValueText())
        }
        .font(.caption2)
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(color.opacity(0.2))
        .cornerRadius(4)
    }
    
    private func getValueText() -> String {
        let adjustedValue = isReversed ? -value : value
        return adjustedValue > 0 ? "+\(adjustedValue)" : "\(adjustedValue)"
    }
}

struct FeedingView_Previews: PreviewProvider {
    static var previews: some View {
        FeedingView(pet: Pet(), gameManager: GameManager())
    }
}
