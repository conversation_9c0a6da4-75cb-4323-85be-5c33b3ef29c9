import SwiftUI

struct StatsView: View {
    let pet: Pet
    let gameManager: GameManager
    @Environment(\.dismiss) private var dismiss
    @State private var showingResetAlert = false
    
    var body: some View {
        NavigationView {
            List {
                Section("宠物信息") {
                    HStack {
                        Text("名字")
                        Spacer()
                        Text(pet.name)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("阶段")
                        Spacer()
                        HStack {
                            Text(pet.stage.emoji)
                            Text(pet.stage.name)
                        }
                        .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("年龄")
                        Spacer()
                        Text("\(pet.age) 小时")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("出生时间")
                        Spacer()
                        Text(pet.birthTime, style: .date)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("下次进化")
                        Spacer()
                        Text(gameManager.getTimeToNextStage())
                            .foregroundColor(.secondary)
                    }
                }
                
                Section("当前状态") {
                    StatRow(title: "饥饿度", value: pet.hunger, maxValue: 100, isReversed: true)
                    StatRow(title: "快乐度", value: pet.happiness, maxValue: 100)
                    StatRow(title: "健康度", value: pet.health, maxValue: 100)
                    StatRow(title: "精力", value: pet.energy, maxValue: 100)
                    
                    HStack {
                        Text("情绪")
                        Spacer()
                        HStack {
                            Text(pet.mood.emoji)
                            Text(getMoodDescription())
                        }
                        .foregroundColor(.secondary)
                    }
                }
                
                Section("统计数据") {
                    HStack {
                        Text("总喂食次数")
                        Spacer()
                        Text("\(pet.totalFeedings)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("总互动次数")
                        Spacer()
                        Text("\(pet.totalInteractions)")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("存活时间")
                        Spacer()
                        Text(getLifeTimeDescription())
                            .foregroundColor(.secondary)
                    }
                }
                
                Section("游戏管理") {
                    Button("重置游戏") {
                        showingResetAlert = true
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("宠物统计")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
            .alert("重置游戏", isPresented: $showingResetAlert) {
                Button("取消", role: .cancel) { }
                Button("重置", role: .destructive) {
                    gameManager.resetGame()
                    dismiss()
                }
            } message: {
                Text("这将删除当前宠物并创建一个新的。此操作无法撤销。")
            }
        }
    }
    
    private func getMoodDescription() -> String {
        switch pet.mood {
        case .happy:
            return "开心"
        case .normal:
            return "正常"
        case .sad:
            return "伤心"
        case .sick:
            return "生病"
        case .sleeping:
            return "困倦"
        }
    }
    
    private func getLifeTimeDescription() -> String {
        let timeAlive = Date().timeIntervalSince(pet.birthTime)
        let days = Int(timeAlive) / 86400
        let hours = (Int(timeAlive) % 86400) / 3600
        let minutes = (Int(timeAlive) % 3600) / 60
        
        if days > 0 {
            return "\(days)天 \(hours)小时"
        } else if hours > 0 {
            return "\(hours)小时 \(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
}

struct StatRow: View {
    let title: String
    let value: Int
    let maxValue: Int
    let isReversed: Bool
    
    init(title: String, value: Int, maxValue: Int, isReversed: Bool = false) {
        self.title = title
        self.value = value
        self.maxValue = maxValue
        self.isReversed = isReversed
    }
    
    var body: some View {
        HStack {
            Text(title)
            Spacer()
            HStack {
                Text("\(value)/\(maxValue)")
                    .foregroundColor(.secondary)
                
                // 状态指示器
                Circle()
                    .fill(getStatusColor())
                    .frame(width: 8, height: 8)
            }
        }
    }
    
    private func getStatusColor() -> Color {
        let percentage = Double(value) / Double(maxValue)
        let adjustedPercentage = isReversed ? 1.0 - percentage : percentage
        
        if adjustedPercentage > 0.7 {
            return .green
        } else if adjustedPercentage > 0.4 {
            return .yellow
        } else {
            return .red
        }
    }
}

struct StatsView_Previews: PreviewProvider {
    static var previews: some View {
        StatsView(pet: Pet(), gameManager: GameManager())
    }
}
