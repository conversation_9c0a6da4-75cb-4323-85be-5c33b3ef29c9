import SwiftUI

struct PetView: View {
    @ObservedObject var pet: Pet
    let onTap: () -> Void
    
    @State private var isAnimating = false
    @State private var bounceAnimation = false
    @State private var glowAnimation = false
    
    var body: some View {
        VStack(spacing: 15) {
            // 主要宠物显示
            ZStack {
                // 背景光晕效果（当宠物开心时）
                if pet.mood == .happy {
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [Color.yellow.opacity(0.3), Color.clear]),
                                center: .center,
                                startRadius: 10,
                                endRadius: 80
                            )
                        )
                        .scaleEffect(glowAnimation ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 2.0).repeatForever(autoreverses: true), value: glowAnimation)
                        .onAppear {
                            glowAnimation = true
                        }
                }
                
                // 宠物主体
                Text(getPetEmoji())
                    .font(.system(size: 120))
                    .scaleEffect(getScaleEffect())
                    .rotationEffect(.degrees(getRotationEffect()))
                    .offset(y: bounceAnimation ? -10 : 0)
                    .animation(.easeInOut(duration: 0.6).repeatForever(autoreverses: true), value: bounceAnimation)
                    .onTapGesture {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                            isAnimating = true
                        }
                        
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            isAnimating = false
                        }
                        
                        onTap()
                    }
                    .onAppear {
                        startIdleAnimation()
                    }
                
                // 状态指示器
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        StatusIndicator(pet: pet)
                    }
                }
            }
            .frame(width: 160, height: 160)
            
            // 宠物信息
            VStack(spacing: 5) {
                Text(pet.name)
                    .font(.title2)
                    .fontWeight(.bold)
                
                HStack(spacing: 8) {
                    Text(pet.stage.name)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.2))
                        .cornerRadius(8)
                    
                    Text(pet.mood.emoji)
                        .font(.caption)
                    
                    Text(getMoodText())
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemGray6))
                .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
    }
    
    private func getPetEmoji() -> String {
        // 根据不同状态返回不同的表情
        switch pet.mood {
        case .sick:
            return getSickEmoji()
        case .sleeping:
            return getSleepingEmoji()
        default:
            return pet.stage.emoji
        }
    }
    
    private func getSickEmoji() -> String {
        switch pet.stage {
        case .egg: return "🥚"
        case .baby: return "🤒"
        case .child: return "🤒"
        case .teen: return "🤒"
        case .adult: return "🤒"
        case .elder: return "🤒"
        }
    }
    
    private func getSleepingEmoji() -> String {
        switch pet.stage {
        case .egg: return "🥚"
        case .baby: return "😴"
        case .child: return "😴"
        case .teen: return "😴"
        case .adult: return "😴"
        case .elder: return "😴"
        }
    }
    
    private func getScaleEffect() -> CGFloat {
        if isAnimating {
            return 1.2
        }
        
        switch pet.mood {
        case .happy:
            return 1.1
        case .sad:
            return 0.9
        case .sick:
            return 0.95
        default:
            return 1.0
        }
    }
    
    private func getRotationEffect() -> Double {
        if isAnimating {
            return 15
        }
        
        switch pet.mood {
        case .sick:
            return -5
        default:
            return 0
        }
    }
    
    private func getMoodText() -> String {
        switch pet.mood {
        case .happy: return "开心"
        case .normal: return "正常"
        case .sad: return "伤心"
        case .sick: return "生病"
        case .sleeping: return "困倦"
        }
    }
    
    private func startIdleAnimation() {
        // 根据宠物状态决定是否播放空闲动画
        switch pet.mood {
        case .happy:
            bounceAnimation = true
        case .sleeping:
            // 睡觉时不播放弹跳动画
            bounceAnimation = false
        default:
            // 随机决定是否播放动画
            bounceAnimation = Bool.random()
        }
    }
}

struct StatusIndicator: View {
    @ObservedObject var pet: Pet
    
    var body: some View {
        VStack(spacing: 2) {
            // 紧急状态指示器
            if pet.hunger > 80 {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.red)
                    .font(.caption)
            }
            
            if pet.health < 30 {
                Image(systemName: "cross.fill")
                    .foregroundColor(.red)
                    .font(.caption)
            }
            
            if pet.happiness < 20 {
                Image(systemName: "heart.slash.fill")
                    .foregroundColor(.orange)
                    .font(.caption)
            }
            
            if pet.energy < 20 {
                Image(systemName: "battery.0")
                    .foregroundColor(.yellow)
                    .font(.caption)
            }
        }
        .padding(4)
        .background(Color.black.opacity(0.7))
        .cornerRadius(6)
        .opacity(hasWarnings() ? 1.0 : 0.0)
        .animation(.easeInOut(duration: 0.3), value: hasWarnings())
    }
    
    private func hasWarnings() -> Bool {
        return pet.hunger > 80 || pet.health < 30 || pet.happiness < 20 || pet.energy < 20
    }
}

struct PetView_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            PetView(pet: Pet()) {
                print("Pet tapped!")
            }

            // 预览不同状态的宠物
            let happyPet = Pet()
            happyPet.happiness = 90
            happyPet.mood = .happy

            PetView(pet: happyPet) {
                print("Happy pet tapped!")
            }
        }
        .padding()
    }
}
